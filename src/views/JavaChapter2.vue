<template>
  <div class="java-chapter2">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <h1 class="chapter-title">第二章：Java 模块系统 (JPMS)</h1>
          <p class="chapter-subtitle">深入理解 Project Jigsaw 与模块化架构</p>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
            <span class="progress-text">{{ progress }}% 完成</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: 模块化背景 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="模块化背景：为何需要模块系统？"
                :concept-data="moduleBackgroundData"
                @interaction="handleInteraction"
              >
                <div class="jar-hell-demo">
                  <h3>🔥 JAR Hell 问题演示</h3>
                  <div class="problem-showcase">
                    <div class="before-modules">
                      <h4>传统 Classpath 的问题</h4>
                      <div class="jar-conflicts">
                        <div class="jar-item conflict">
                          <span class="jar-name">log4j-1.2.jar</span>
                          <span class="conflict-badge">冲突!</span>
                        </div>
                        <div class="jar-item conflict">
                          <span class="jar-name">log4j-2.0.jar</span>
                          <span class="conflict-badge">冲突!</span>
                        </div>
                        <div class="jar-item">
                          <span class="jar-name">commons-lang.jar</span>
                        </div>
                        <div class="jar-item">
                          <span class="jar-name">spring-core.jar</span>
                        </div>
                      </div>
                      <div class="problem-description">
                        <p>❌ 版本冲突不可预测</p>
                        <p>❌ 依赖关系隐藏</p>
                        <p>❌ 内部API被滥用</p>
                      </div>
                    </div>

                    <div class="arrow-separator">→</div>

                    <div class="after-modules">
                      <h4>模块化解决方案</h4>
                      <div class="module-graph">
                        <div class="module-item platform">
                          <span class="module-name">java.base</span>
                          <span class="module-type">平台模块</span>
                        </div>
                        <div class="module-item application">
                          <span class="module-name">my.app</span>
                          <span class="module-type">应用模块</span>
                        </div>
                        <div class="module-item automatic">
                          <span class="module-name">commons.lang3</span>
                          <span class="module-type">自动模块</span>
                        </div>
                      </div>
                      <div class="solution-description">
                        <p>✅ 明确的依赖声明</p>
                        <p>✅ 强封装保护</p>
                        <p>✅ 启动时验证</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- F. 项目实践踩坑与解决方案 -->
                <div class="real-world-problems">
                  <h3>🔧 项目实践踩坑与解决方案</h3>

                  <div class="problem-section">
                    <h4>💥 常见问题描述</h4>
                    <div class="problem-cards">
                      <div class="problem-card critical">
                        <div class="problem-header">
                          <span class="problem-icon">🔥</span>
                          <h5>大型企业项目的JAR Hell噩梦</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >企业级微服务项目中，不同服务使用了不同版本的Spring、Jackson、Guava等常用库，在服务间调用或共享库时出现ClassNotFoundException、NoSuchMethodError等运行时异常。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >本地开发正常，但部署到测试环境后出现"类找不到"或"方法不存在"错误，排查困难，影响交付进度。
                          </p>
                        </div>
                      </div>

                      <div class="problem-card warning">
                        <div class="problem-header">
                          <span class="problem-icon">🐛</span>
                          <h5>传统Classpath的依赖传递混乱</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >项目依赖了A库，A库又依赖了B库的1.0版本，但项目直接依赖了B库的2.0版本，导致运行时使用了错误的版本，出现不可预测的行为。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >Maven/Gradle构建成功，但运行时出现奇怪的业务逻辑错误，调试时发现使用的类版本与预期不符。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="root-cause-analysis">
                    <h4>🔍 问题根源分析</h4>
                    <div class="analysis-content">
                      <div class="cause-item">
                        <span class="cause-icon">🎯</span>
                        <div>
                          <h5>Classpath机制的根本缺陷</h5>
                          <p>
                            传统的Classpath是一个扁平的字符串列表，JVM按顺序搜索类，无法处理同名类的多个版本，也无法表达复杂的依赖关系，这是架构层面的根本问题。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">📦</span>
                        <div>
                          <h5>JAR文件缺乏元数据</h5>
                          <p>
                            传统JAR文件只是类文件的压缩包，缺乏版本信息、依赖声明、API边界等关键元数据，导致运行时无法进行有效的依赖管理和冲突检测。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">🏗️</span>
                        <div>
                          <h5>企业项目复杂度爆炸</h5>
                          <p>
                            现代企业项目动辄依赖数百个第三方库，每个库又有自己的依赖树，形成了复杂的依赖网络，传统工具无法有效管理这种复杂性。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="solutions-section">
                    <h4>💡 业界主流解决方案与权衡</h4>
                    <div class="solutions-grid">
                      <div class="solution-card recommended">
                        <div class="solution-header">
                          <span class="solution-icon">🏗️</span>
                          <h5>渐进式模块化迁移</h5>
                          <span class="solution-badge best">推荐方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >采用自底向上的迁移策略，先将核心库模块化，然后逐步扩展到业务模块，利用自动模块作为过渡方案。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>风险可控，可以逐步验证效果</li>
                                <li>与现有系统兼容性好</li>
                                <li>团队学习曲线平缓</li>
                                <li>可以立即获得部分收益</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>迁移周期较长</li>
                                <li>需要维护混合架构</li>
                                <li>可能出现模块边界设计问题</li>
                                <li>需要额外的工程投入</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合大多数企业级项目，能够在稳定性和现代化之间取得平衡，是目前最主流的模块化迁移策略。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card alternative">
                        <div class="solution-header">
                          <span class="solution-icon">🔧</span>
                          <h5>依赖管理工具强化</h5>
                          <span class="solution-badge stable">技术方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >使用Maven/Gradle的高级特性（如依赖锁定、BOM管理、冲突解决策略）加强依赖管理，配合Docker容器化部署确保环境一致性。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>无需修改现有代码架构</li>
                                <li>可以立即实施</li>
                                <li>工具链成熟，社区支持好</li>
                                <li>学习成本相对较低</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>治标不治本，根本问题依然存在</li>
                                <li>依赖外部工具，增加复杂度</li>
                                <li>无法获得模块化的封装优势</li>
                                <li>长期维护成本较高</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong>适合短期内无法进行架构升级的项目，能够缓解JAR
                              Hell问题，但无法获得模块化的根本性优势。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card temporary">
                        <div class="solution-header">
                          <span class="solution-icon">🔒</span>
                          <h5>版本锁定与隔离策略</h5>
                          <span class="solution-badge conservative">保守方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >严格锁定所有依赖版本，使用类加载器隔离或OSGi等技术实现运行时隔离，避免版本冲突。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>可以彻底避免版本冲突</li>
                                <li>运行时行为可预测</li>
                                <li>适合对稳定性要求极高的场景</li>
                                <li>可以支持多版本共存</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>严重限制了依赖升级的灵活性</li>
                                <li>可能错过重要的安全更新</li>
                                <li>增加了系统复杂度</li>
                                <li>性能开销较大</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合对稳定性要求极高的关键业务系统，但会牺牲灵活性和可维护性，不建议作为长期策略。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: 模块基本语法 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="模块的基本语法 (module-info.java)"
                :concept-data="moduleInfoData"
                @interaction="handleInteraction"
              >
                <ModuleCodePlayground
                  :examples="moduleInfoExamples"
                  title="module-info.java 语法实践"
                  @code-run="handleCodeRun"
                />

                <!-- F. 项目实践踩坑与解决方案 -->
                <div class="real-world-problems">
                  <h3>🔧 项目实践踩坑与解决方案</h3>

                  <div class="problem-section">
                    <h4>💥 常见问题描述</h4>
                    <div class="problem-cards">
                      <div class="problem-card critical">
                        <div class="problem-header">
                          <span class="problem-icon">📝</span>
                          <h5>module-info.java配置错误导致编译失败</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >团队成员在编写module-info.java时，经常出现模块名不规范、exports/requires语句错误、或者忘记声明传递依赖，导致编译失败或运行时模块找不到。
                          </p>
                          <p>
                            <strong>表现：</strong>编译器报错"module not found"、"package is not
                            visible"，或者运行时出现"Module X does not read module Y"等错误。
                          </p>
                        </div>
                      </div>

                      <div class="problem-card warning">
                        <div class="problem-header">
                          <span class="problem-icon">🔄</span>
                          <h5>循环依赖检测与解决困难</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >在大型项目重构为模块化时，发现原有代码存在循环依赖，模块系统在编译时就会检测并拒绝这种依赖关系，导致重构工作量激增。
                          </p>
                          <p>
                            <strong>表现：</strong>编译器报错"cyclic dependence
                            involving"，需要重新设计模块边界和依赖关系，影响项目进度。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="root-cause-analysis">
                    <h4>🔍 问题根源分析</h4>
                    <div class="analysis-content">
                      <div class="cause-item">
                        <span class="cause-icon">📚</span>
                        <div>
                          <h5>模块化思维转换困难</h5>
                          <p>
                            开发者习惯了传统的包级别思考，需要转换到模块级别的架构思维。模块边界的设计需要考虑业务领域、技术层次、团队组织等多个维度。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">🔧</span>
                        <div>
                          <h5>工具链支持不完善</h5>
                          <p>
                            虽然IDE对模块化有基本支持，但在复杂场景下（如多模块项目、传递依赖分析、循环依赖检测）的工具支持仍不够完善，开发者需要手动排查问题。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">🏗️</span>
                        <div>
                          <h5>遗留代码架构约束</h5>
                          <p>
                            现有代码往往不是按照模块化原则设计的，存在紧耦合、循环依赖等问题，直接应用模块化会暴露这些架构问题，需要大量重构工作。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="solutions-section">
                    <h4>💡 业界主流解决方案与权衡</h4>
                    <div class="solutions-grid">
                      <div class="solution-card recommended">
                        <div class="solution-header">
                          <span class="solution-icon">📋</span>
                          <h5>模块化设计规范与模板</h5>
                          <span class="solution-badge best">推荐方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >建立团队级别的模块化设计规范，包括命名约定、依赖管理原则、模块边界划分指南，并提供标准的module-info.java模板。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>减少配置错误，提高开发效率</li>
                                <li>保证团队代码风格一致性</li>
                                <li>便于新成员快速上手</li>
                                <li>可以积累最佳实践经验</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>需要投入时间制定和维护规范</li>
                                <li>可能限制某些灵活的设计方案</li>
                                <li>需要团队达成共识</li>
                                <li>规范更新需要同步培训</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合所有采用模块化的团队，前期投入换取长期的开发效率提升，ROI很高。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card alternative">
                        <div class="solution-header">
                          <span class="solution-icon">🔍</span>
                          <h5>自动化依赖分析工具</h5>
                          <span class="solution-badge stable">技术方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >使用jdeps等工具分析现有代码的依赖关系，自动生成module-info.java草稿，并集成到CI/CD流程中进行持续的依赖健康检查。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>自动化程度高，减少人工错误</li>
                                <li>可以快速分析大型项目</li>
                                <li>提供可视化的依赖关系图</li>
                                <li>能够及早发现循环依赖</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>工具学习和配置成本</li>
                                <li>生成的配置可能需要手动调整</li>
                                <li>无法解决架构层面的问题</li>
                                <li>对复杂场景支持有限</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合有一定技术实力的团队，能够显著提升模块化迁移效率，但需要与人工设计相结合。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card temporary">
                        <div class="solution-header">
                          <span class="solution-icon">🔄</span>
                          <h5>分阶段重构策略</h5>
                          <span class="solution-badge conservative">渐进方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >将模块化重构分为多个阶段，先解决循环依赖等架构问题，再逐步引入模块化，每个阶段都有明确的目标和验收标准。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>风险可控，每个阶段都可验证</li>
                                <li>可以逐步积累经验</li>
                                <li>不会影响现有功能</li>
                                <li>便于团队学习和适应</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>整体周期较长</li>
                                <li>需要维护多种架构状态</li>
                                <li>可能出现重复工作</li>
                                <li>收益实现较慢</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合大型遗留系统或风险承受能力较低的项目，能够确保稳定性但需要更长的时间投入。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: 模块加载与类型 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="模块的加载与类型"
                :concept-data="moduleTypesData"
                @interaction="handleInteraction"
              >
                <ModuleSystemAnimation />
                <div class="module-types-detail">
                  <h3>🏷️ 四种模块类型详解</h3>
                  <div class="types-grid">
                    <div class="type-card platform">
                      <div class="type-header">
                        <span class="type-icon">🏛️</span>
                        <h4>平台模块</h4>
                        <span class="type-badge">Platform</span>
                      </div>
                      <div class="type-content">
                        <p><strong>定义：</strong>JDK 内置的模块</p>
                        <p><strong>示例：</strong>java.base, java.sql, java.xml</p>
                        <p><strong>特点：</strong>完全模块化，强封装</p>
                        <div class="type-examples">
                          <code>java.base</code> - 所有模块的基础<br />
                          <code>java.sql</code> - 数据库访问<br />
                          <code>java.xml</code> - XML 处理
                        </div>
                      </div>
                    </div>

                    <div class="type-card application">
                      <div class="type-header">
                        <span class="type-icon">📱</span>
                        <h4>应用模块</h4>
                        <span class="type-badge">Application</span>
                      </div>
                      <div class="type-content">
                        <p><strong>定义：</strong>用户编写的标准模块</p>
                        <p><strong>要求：</strong>必须有 module-info.java</p>
                        <p><strong>特点：</strong>明确的依赖和导出声明</p>
                        <div class="type-examples">
                          完整的模块描述符<br />
                          清晰的 API 边界<br />
                          可靠的配置
                        </div>
                      </div>
                    </div>

                    <div class="type-card automatic">
                      <div class="type-header">
                        <span class="type-icon">🔄</span>
                        <h4>自动模块</h4>
                        <span class="type-badge">Automatic</span>
                      </div>
                      <div class="type-content">
                        <p><strong>定义：</strong>模块路径上的传统 JAR</p>
                        <p><strong>行为：</strong>导出所有包，读取所有模块</p>
                        <p><strong>用途：</strong>迁移过程中的过渡方案</p>
                        <div class="type-examples">
                          名称由 JAR 文件名推断<br />
                          最大兼容性策略<br />
                          临时解决方案
                        </div>
                      </div>
                    </div>

                    <div class="type-card unnamed">
                      <div class="type-header">
                        <span class="type-icon">❓</span>
                        <h4>未命名模块</h4>
                        <span class="type-badge">Unnamed</span>
                      </div>
                      <div class="type-content">
                        <p><strong>定义：</strong>类路径上的所有 JAR</p>
                        <p><strong>限制：</strong>具名模块不能依赖它</p>
                        <p><strong>特点：</strong>传统的 classpath 行为</p>
                        <div class="type-examples">
                          单一虚拟模块<br />
                          向后兼容<br />
                          逐步淘汰
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- F. 项目实践踩坑与解决方案 -->
                <div class="real-world-problems">
                  <h3>🔧 项目实践踩坑与解决方案</h3>

                  <div class="problem-section">
                    <h4>💥 常见问题描述</h4>
                    <div class="problem-cards">
                      <div class="problem-card critical">
                        <div class="problem-header">
                          <span class="problem-icon">🔄</span>
                          <h5>自动模块命名冲突与不稳定性</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >项目依赖了多个第三方JAR作为自动模块，但这些JAR的自动生成模块名出现冲突，或者JAR文件名变化导致模块名变化，破坏了依赖关系。
                          </p>
                          <p>
                            <strong>表现：</strong>编译时出现"module name
                            conflict"错误，或者升级依赖版本后出现"module not
                            found"，需要修改所有相关的module-info.java文件。
                          </p>
                        </div>
                      </div>

                      <div class="problem-card warning">
                        <div class="problem-header">
                          <span class="problem-icon">🌐</span>
                          <h5>未命名模块与具名模块的访问限制</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >在混合架构中，具名模块无法访问未命名模块（classpath上的JAR），导致某些遗留代码或第三方库无法在模块化应用中使用。
                          </p>
                          <p>
                            <strong>表现：</strong>运行时出现"package is not
                            visible"错误，即使类在classpath上存在，模块化代码也无法访问。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="root-cause-analysis">
                    <h4>🔍 问题根源分析</h4>
                    <div class="analysis-content">
                      <div class="cause-item">
                        <span class="cause-icon">🎯</span>
                        <div>
                          <h5>模块系统的严格性设计</h5>
                          <p>
                            Java模块系统为了确保可靠性，采用了严格的访问控制规则。具名模块不能依赖未命名模块是为了防止模块化应用依赖不可控的classpath内容。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">📦</span>
                        <div>
                          <h5>自动模块的过渡性质</h5>
                          <p>
                            自动模块是为了兼容性而设计的过渡方案，其模块名由JAR文件名推断，缺乏稳定性。这种设计在迁移期间有用，但不适合长期依赖。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">🏗️</span>
                        <div>
                          <h5>生态系统迁移的不同步性</h5>
                          <p>
                            不同的开源项目模块化进度不一致，有些已经提供了正式的module-info.java，有些仍然是传统JAR，导致依赖管理复杂化。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="solutions-section">
                    <h4>💡 业界主流解决方案与权衡</h4>
                    <div class="solutions-grid">
                      <div class="solution-card recommended">
                        <div class="solution-header">
                          <span class="solution-icon">🎯</span>
                          <h5>分层架构与模块边界设计</h5>
                          <span class="solution-badge best">推荐方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >设计清晰的分层架构，将模块化部分与非模块化部分隔离，通过服务接口和依赖注入实现松耦合，逐步扩大模块化范围。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>架构清晰，职责分明</li>
                                <li>可以逐步迁移，风险可控</li>
                                <li>便于测试和维护</li>
                                <li>为未来完全模块化做准备</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>需要重新设计架构</li>
                                <li>增加了抽象层的复杂度</li>
                                <li>可能影响性能</li>
                                <li>需要团队具备架构设计能力</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合中大型项目，能够在模块化收益和迁移成本之间取得平衡，是长期可持续的解决方案。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card alternative">
                        <div class="solution-header">
                          <span class="solution-icon">🔧</span>
                          <h5>自定义模块描述符注入</h5>
                          <span class="solution-badge stable">技术方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >为第三方JAR创建自定义的module-info.java，通过Maven/Gradle插件或手动方式注入到JAR中，将自动模块转换为正式模块。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>获得完整的模块化控制</li>
                                <li>模块名稳定，不受JAR文件名影响</li>
                                <li>可以精确控制导出和依赖</li>
                                <li>提升应用的可靠性</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>需要深入了解第三方库的内部结构</li>
                                <li>维护成本高，需要跟进库的更新</li>
                                <li>可能与库的官方模块化冲突</li>
                                <li>增加构建流程的复杂度</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合对特定第三方库有深度依赖的项目，能够获得最大的模块化收益，但需要投入较多的维护成本。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card temporary">
                        <div class="solution-header">
                          <span class="solution-icon">🔄</span>
                          <h5>混合部署策略</h5>
                          <span class="solution-badge conservative">过渡方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >在同一应用中同时使用模块路径和类路径，通过--add-modules、--add-exports等JVM参数解决访问限制问题。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>可以立即解决兼容性问题</li>
                                <li>无需修改现有代码</li>
                                <li>保持与遗留系统的兼容性</li>
                                <li>实施成本最低</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>无法获得完整的模块化收益</li>
                                <li>JVM参数配置复杂</li>
                                <li>破坏了模块系统的封装性</li>
                                <li>不是长期可持续的方案</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合作为短期过渡方案或紧急修复手段，但应该制定明确的迁移计划，避免长期依赖这种方式。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: 构建和运行 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="构建和运行模块化应用"
                :concept-data="buildRunData"
                @interaction="handleInteraction"
              >
                <div class="command-showcase">
                  <h3>🛠️ 模块化命令行工具</h3>
                  <div class="command-sections">
                    <div class="command-section">
                      <h4>编译模块</h4>
                      <div class="command-block">
                        <code>javac -d out --module-source-path src -m wgjd.discovery</code>
                      </div>
                      <p class="command-explanation">
                        使用 <code>--module-source-path</code> 指定模块源码路径，<code>-m</code>
                        指定要编译的模块
                      </p>
                    </div>

                    <div class="command-section">
                      <h4>运行模块</h4>
                      <div class="command-block">
                        <code>java --module-path out -m wgjd.discovery/wgjd.discovery.Main</code>
                      </div>
                      <p class="command-explanation">
                        使用 <code>--module-path</code> 指定模块路径，<code>-m</code>
                        指定主模块和主类
                      </p>
                    </div>

                    <div class="command-section">
                      <h4>反射访问控制</h4>
                      <div class="command-block">
                        <code>java --add-opens java.base/sun.net=my.framework</code>
                      </div>
                      <p class="command-explanation">
                        使用 <code>--add-opens</code> 允许特定模块通过反射访问内部包
                      </p>
                    </div>

                    <div class="command-section">
                      <h4>静态访问控制</h4>
                      <div class="command-block">
                        <code>java --add-exports java.base/sun.misc=my.module</code>
                      </div>
                      <p class="command-explanation">
                        使用 <code>--add-exports</code> 允许特定模块访问未导出的包
                      </p>
                    </div>
                  </div>
                </div>

                <!-- F. 项目实践踩坑与解决方案 -->
                <div class="real-world-problems">
                  <h3>🔧 项目实践踩坑与解决方案</h3>

                  <div class="problem-section">
                    <h4>💥 常见问题描述</h4>
                    <div class="problem-cards">
                      <div class="problem-card critical">
                        <div class="problem-header">
                          <span class="problem-icon">⚙️</span>
                          <h5>复杂JVM参数配置导致的部署困难</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >模块化应用需要大量的--add-opens、--add-exports、--add-modules等JVM参数才能正常运行，导致启动脚本复杂，不同环境配置不一致。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >本地开发正常，但在Docker容器或生产环境中启动失败，运维团队难以理解和维护这些复杂的JVM参数。
                          </p>
                        </div>
                      </div>

                      <div class="problem-card warning">
                        <div class="problem-header">
                          <span class="problem-icon">🔍</span>
                          <h5>模块路径与类路径的混合配置混乱</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >在迁移过程中，同时使用--module-path和--class-path，但配置不当导致类加载顺序混乱，出现意外的类版本被加载。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >应用行为不可预测，同样的代码在不同的启动配置下表现不同，调试困难。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="solutions-section">
                    <h4>💡 业界主流解决方案与权衡</h4>
                    <div class="solutions-grid">
                      <div class="solution-card recommended">
                        <div class="solution-header">
                          <span class="solution-icon">🐳</span>
                          <h5>容器化标准化部署</h5>
                          <span class="solution-badge best">推荐方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >将JVM参数和模块配置封装在Docker镜像中，通过环境变量控制不同环境的差异，确保所有环境使用相同的运行时配置。
                          </p>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合云原生应用，能够确保环境一致性，是现代Java应用部署的标准做法。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card alternative">
                        <div class="solution-header">
                          <span class="solution-icon">📋</span>
                          <h5>启动脚本模板化</h5>
                          <span class="solution-badge stable">工程方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >创建标准化的启动脚本模板，将常用的JVM参数组合封装成可重用的配置文件，通过脚本参数控制不同的运行模式。
                          </p>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合传统部署环境，能够简化运维工作，但需要维护多套脚本模板。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 5: 架构设计 -->
            <section id="topic-4" class="topic-section" ref="topic4">
              <ExpandableSection
                title="模块化架构设计"
                :concept-data="architectureData"
                @interaction="handleInteraction"
              >
                <div class="architecture-patterns">
                  <h3>🏗️ 模块化设计模式</h3>

                  <!-- 分裂包问题 -->
                  <div class="pattern-detail">
                    <div class="pattern-header">
                      <span class="pattern-icon">💥</span>
                      <h4>分裂包 (Split Packages) 问题</h4>
                      <span class="severity-badge danger">严重问题</span>
                    </div>
                    <div class="pattern-content">
                      <div class="problem-demo">
                        <div class="split-package-example">
                          <div class="module-box">
                            <h5>模块 A</h5>
                            <div class="package-item">com.example.util.StringUtils</div>
                          </div>
                          <div class="conflict-indicator">❌</div>
                          <div class="module-box">
                            <h5>模块 B</h5>
                            <div class="package-item">com.example.util.DateUtils</div>
                          </div>
                        </div>
                        <p class="problem-explanation">
                          <strong>问题：</strong>同一个包
                          <code>com.example.util</code>
                          分散在两个模块中，这在模块系统中是严格禁止的！
                        </p>
                        <div class="solution-box">
                          <h6>解决方案：</h6>
                          <ul>
                            <li>重构：将分裂的包合并到一个模块中</li>
                            <li>重命名：修改其中一个包的名称</li>
                            <li>创建共享模块：将公共包提取到独立模块</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 多版本JAR -->
                  <div class="pattern-detail">
                    <div class="pattern-header">
                      <span class="pattern-icon">📦</span>
                      <h4>多版本 JAR (Multi-Release JAR)</h4>
                      <span class="severity-badge success">最佳实践</span>
                    </div>
                    <div class="pattern-content">
                      <div class="mr-jar-structure">
                        <h5>MR-JAR 文件结构</h5>
                        <div class="file-tree">
                          <div class="tree-item">📁 my-library.jar</div>
                          <div class="tree-item level-1">
                            📄 com/mycompany/MyClass.class <span class="version-tag">Java 8</span>
                          </div>
                          <div class="tree-item level-1">📁 META-INF/</div>
                          <div class="tree-item level-2">📁 versions/</div>
                          <div class="tree-item level-3">📁 11/</div>
                          <div class="tree-item level-4">
                            📄 com/mycompany/MyClass.class <span class="version-tag">Java 11</span>
                          </div>
                          <div class="tree-item level-3">📁 17/</div>
                          <div class="tree-item level-4">
                            📄 com/mycompany/MyClass.class <span class="version-tag">Java 17</span>
                          </div>
                        </div>
                        <p class="mr-jar-explanation">
                          高版本 JVM 会优先加载对应版本的实现，低版本 JVM
                          使用根目录的默认实现，实现完美的向后兼容。
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 6: jlink工具 -->
            <section id="topic-5" class="topic-section" ref="topic5">
              <ExpandableSection
                title="jlink：模块化的终极优势"
                :concept-data="jlinkData"
                @interaction="handleInteraction"
              >
                <div class="jlink-showcase">
                  <h3>🔗 jlink 工具演示</h3>

                  <div class="jlink-comparison">
                    <div class="traditional-deployment">
                      <h4>传统部署方式</h4>
                      <div class="deployment-stack">
                        <div class="layer jre-layer">
                          <span class="layer-name">完整 JRE</span>
                          <span class="layer-size">~200MB</span>
                        </div>
                        <div class="layer app-layer">
                          <span class="layer-name">应用程序</span>
                          <span class="layer-size">~50MB</span>
                        </div>
                      </div>
                      <div class="total-size traditional">总计: ~250MB</div>
                    </div>

                    <div class="jlink-deployment">
                      <h4>jlink 精简部署</h4>
                      <div class="deployment-stack">
                        <div class="layer custom-runtime">
                          <span class="layer-name">定制运行时</span>
                          <span class="layer-size">~30MB</span>
                        </div>
                        <div class="layer app-layer">
                          <span class="layer-name">应用程序</span>
                          <span class="layer-size">~50MB</span>
                        </div>
                      </div>
                      <div class="total-size optimized">总计: ~80MB</div>
                      <div class="savings">节省 68% 空间！</div>
                    </div>
                  </div>

                  <div class="jlink-commands">
                    <h4>jlink 使用示例</h4>
                    <div class="command-example">
                      <div class="command-block">
                        <code
                          >jlink --add-modules java.base,java.sql,my.app --output my-runtime</code
                        >
                      </div>
                      <p class="command-explanation">
                        创建一个只包含必要模块的自定义运行时，大大减少部署体积
                      </p>
                    </div>

                    <div class="jlink-benefits">
                      <h5>jlink 的优势</h5>
                      <div class="benefits-grid">
                        <div class="benefit-item">
                          <span class="benefit-icon">📦</span>
                          <div>
                            <strong>体积优化</strong>
                            <small>只包含必要模块</small>
                          </div>
                        </div>
                        <div class="benefit-item">
                          <span class="benefit-icon">🔒</span>
                          <div>
                            <strong>安全性提升</strong>
                            <small>减少攻击面</small>
                          </div>
                        </div>
                        <div class="benefit-item">
                          <span class="benefit-icon">🚀</span>
                          <div>
                            <strong>启动加速</strong>
                            <small>更少的类加载</small>
                          </div>
                        </div>
                        <div class="benefit-item">
                          <span class="benefit-icon">🌱</span>
                          <div>
                            <strong>绿色部署</strong>
                            <small>无需预装Java</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- 章节总结与思维导图 -->
            <section id="topic-6" class="topic-section chapter-summary" ref="topic6">
              <ExpandableSection
                title="📊 章节总结与知识体系图"
                :concept-data="chapterSummaryData"
                @interaction="handleInteraction"
              >
                <div class="summary-content">
                  <h3>🎯 本章核心收获</h3>
                  <div class="key-takeaways">
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🔥</span>
                      <div>
                        <h4>解决JAR Hell</h4>
                        <p>通过模块系统彻底解决版本冲突和依赖混乱问题</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🛡️</span>
                      <div>
                        <h4>强封装保护</h4>
                        <p>保护内部API，建立清晰的模块边界</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">⚡</span>
                      <div>
                        <h4>jlink优化</h4>
                        <p>创建精简运行时，大幅减少部署体积</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🏗️</span>
                      <div>
                        <h4>架构升级</h4>
                        <p>从传统classpath向现代模块化架构演进</p>
                      </div>
                    </div>
                  </div>

                  <div class="mindmap-container">
                    <h3>🧠 Java模块系统知识体系图</h3>
                    <div class="mindmap-wrapper">
                      <div id="chapter2-mindmap" class="mermaid-container"></div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import ModuleSystemAnimation from '@/components/ModuleSystemAnimation.vue'
import ModuleCodePlayground from '@/components/ModuleCodePlayground.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)

// 课程主题
const courseTopics = [
  {
    title: '模块化背景',
    description: '理解JAR Hell问题和模块系统的解决方案',
  },
  {
    title: '模块基本语法',
    description: '掌握module-info.java的核心语法',
  },
  {
    title: '模块加载与类型',
    description: '了解四种模块类型和加载机制',
  },
  {
    title: '构建和运行',
    description: '学习模块化应用的编译和运行',
  },
  {
    title: '架构设计',
    description: '掌握模块化设计模式和最佳实践',
  },
  {
    title: 'jlink工具',
    description: '使用jlink创建精简运行时',
  },
  {
    title: '章节总结',
    description: '知识体系图和核心收获总结',
  },
]

// 数据定义
const moduleBackgroundData = {
  keyPoints: [
    'JAR Hell：版本冲突和依赖管理混乱',
    'Jigsaw项目：Java平台模块系统的官方名称',
    '强封装：保护内部API，防止滥用',
    '可靠配置：启动时验证依赖关系',
  ],
}

const moduleInfoData = {
  keyPoints: [
    'module声明：定义模块名称',
    'requires：声明依赖的模块',
    'exports：导出公开的包',
    'requires transitive：传递性依赖',
    'opens：为反射开放包',
  ],
}

const moduleTypesData = {
  keyPoints: [
    '平台模块：JDK内置模块，如java.base',
    '应用模块：有module-info.java的用户模块',
    '自动模块：模块路径上的传统JAR',
    '未命名模块：类路径上的所有JAR',
  ],
}

const buildRunData = {
  keyPoints: [
    '--module-path：指定模块路径',
    '-m：指定主模块和主类',
    '--add-opens：为反射开放包',
    '--add-exports：导出未公开的包',
  ],
}

const architectureData = {
  keyPoints: [
    '分裂包：同一包分散在多个模块中，严格禁止',
    '多版本JAR：同一JAR支持多个Java版本',
    '迁移策略：从classpath到模块的渐进式迁移',
    '设计原则：明确的API边界和依赖关系',
  ],
}

const jlinkData = {
  keyPoints: [
    '自定义运行时：只包含必要的模块',
    '体积优化：从200MB减少到30MB',
    '安全性：减少攻击面',
    '绿色部署：无需预装Java环境',
  ],
}

const chapterSummaryData = {
  keyPoints: [
    '掌握Java模块系统的核心概念和实际应用',
    '理解从JAR Hell到模块化的演进过程',
    '学会使用jlink工具创建精简运行时',
    '建立现代Java应用的架构设计思维',
  ],
}

const moduleInfoExamples = [
  {
    title: '基本模块声明',
    code: `module wgjd.discovery {
    // 依赖声明
    requires java.instrument;
    requires java.sql;

    // API导出
    exports wgjd.discovery;

    // 限定导出
    exports wgjd.discovery.internal to com.another.module;
}`,
    explanation: '展示了模块的基本语法结构',
  },
  {
    title: '传递性依赖',
    code: `module my.library {
    // 传递性依赖：依赖我的模块也会自动依赖java.sql
    requires transitive java.sql;

    // 普通依赖
    requires java.base;

    // 导出API
    exports com.mylib.api;
}`,
    explanation: 'requires transitive会将依赖传递给下游模块',
  },
  {
    title: '反射开放',
    code: `open module my.framework {
    requires java.base;

    // 整个模块对反射开放
    exports com.framework.api;
}

// 或者选择性开放
module my.app {
    requires java.base;

    // 只对特定包开放反射
    opens com.app.internal to spring.core;

    exports com.app.api;
}`,
    explanation: 'open关键字允许反射访问模块内部',
  },
]

// 方法
const scrollToTopic = (index: number) => {
  currentTopic.value = index

  // 滚动到对应的主题部分
  const targetElement = document.getElementById(`topic-${index}`)
  if (targetElement) {
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

const handleInteraction = (type: string) => {
  console.log('Interaction:', type)
}

const handleCodeRun = (data: any) => {
  console.log('Code run:', data)
}

// 初始化Mermaid
onMounted(async () => {
  try {
    console.log('开始初始化 Mermaid...')
    const mermaid = await import('mermaid')
    console.log('Mermaid 模块加载成功:', mermaid)

    mermaid.default.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
    })
    console.log('Mermaid 初始化完成')

    // 延迟渲染以确保DOM已加载
    setTimeout(async () => {
      try {
        console.log('开始渲染 Mermaid 图表...')

        // 创建思维导图内容
        const mindmapContent = `mindmap
  root((Java模块系统))
    背景动机
      JAR Hell
      强封装
      可靠配置
    核心语法
      module声明
      requires依赖
      exports导出
    模块类型
      平台模块
      应用模块
      自动模块
      未命名模块
    构建运行
      javac编译
      java运行
      模块路径
    架构设计
      分裂包
      多版本JAR
      迁移策略
    jlink工具
      精简运行时
      体积优化
      绿色部署`

        const container = document.getElementById('chapter2-mindmap')
        if (container) {
          console.log('找到容器，开始渲染...')
          const { svg } = await mermaid.default.render('chapter2-mindmap-svg', mindmapContent)
          container.innerHTML = svg
          console.log('Mermaid 图表渲染完成')
        } else {
          console.error('未找到思维导图容器')
        }
      } catch (renderError) {
        console.error('Mermaid 渲染错误:', renderError)
      }
    }, 1000) // 增加延迟时间
  } catch (error) {
    console.error('Mermaid 初始化失败:', error)
  }
})
</script>

<style scoped>
/* 基础样式 */
.java-chapter2 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 0;
  text-align: center;
}

.chapter-title {
  font-size: 2.5rem;
  margin: 0 0 1rem 0;
  font-weight: 700;
}

.chapter-subtitle {
  font-size: 1.2rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
}

.progress-bar {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  height: 8px;
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.progress-fill {
  background: white;
  height: 100%;
  border-radius: 25px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -30px;
  right: 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  margin-top: 3rem;
}

.sidebar {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.outline {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.outline h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
}

.outline-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.5rem;
}

.outline-item:hover {
  background: #f8f9fa;
}

.outline-item.active {
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.outline-number {
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.outline-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.outline-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tool-button {
  background: white;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.tool-button:hover {
  background: #f8f9fa;
  border-color: #667eea;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.topic-section {
  margin-bottom: 2rem;
}

.topic-section:last-child {
  margin-bottom: 0;
}

/* JAR Hell 演示样式 */
.jar-hell-demo {
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 2rem 0;
}

.problem-showcase {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: center;
  margin-top: 1.5rem;
}

.before-modules,
.after-modules {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.before-modules h4,
.after-modules h4 {
  margin: 0 0 1rem 0;
  color: #333;
  text-align: center;
}

.jar-conflicts {
  margin-bottom: 1rem;
}

.jar-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin: 0.5rem 0;
  background: #e8f5e8;
  border: 2px solid #4caf50;
  border-radius: 6px;
}

.jar-item.conflict {
  background: #ffebee;
  border-color: #f44336;
}

.conflict-badge {
  background: #f44336;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.arrow-separator {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

.module-graph {
  margin-bottom: 1rem;
}

.module-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin: 0.5rem 0;
  border-radius: 6px;
  border: 2px solid;
}

.module-item.platform {
  background: #e3f2fd;
  border-color: #2196f3;
}

.module-item.application {
  background: #e8f5e8;
  border-color: #4caf50;
}

.module-item.automatic {
  background: #fff3e0;
  border-color: #ff9800;
}

.module-type {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
}

.problem-description,
.solution-description {
  margin-top: 1rem;
}

.problem-description p,
.solution-description p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

/* 模块类型详解样式 */
.module-types-detail {
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 2rem 0;
}

.types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.type-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
}

.type-card.platform {
  border-left-color: #2196f3;
}

.type-card.application {
  border-left-color: #4caf50;
}

.type-card.automatic {
  border-left-color: #ff9800;
}

.type-card.unnamed {
  border-left-color: #9e9e9e;
}

.type-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.type-icon {
  font-size: 1.5rem;
}

.type-header h4 {
  margin: 0;
  flex: 1;
  color: #333;
}

.type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  background: #e9ecef;
  color: #495057;
}

.type-content p {
  margin: 0.5rem 0;
  color: #666;
  line-height: 1.6;
}

.type-examples {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #555;
  line-height: 1.6;
}

.type-examples code {
  background: #e9ecef;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

@media (max-width: 768px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .sidebar {
    position: static;
    order: -1;
  }

  .chapter-title {
    font-size: 2rem;
  }

  .problem-showcase {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .arrow-separator {
    transform: rotate(90deg);
  }

  .types-grid {
    grid-template-columns: 1fr;
  }
}

/* 命令行工具样式 */
.command-showcase {
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 2rem 0;
}

.command-sections {
  margin-top: 1.5rem;
}

.command-section {
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.command-section h4 {
  margin: 0 0 1rem 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.command-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  overflow-x: auto;
  margin-bottom: 1rem;
}

.command-block code {
  color: #e2e8f0;
  background: none;
}

.command-explanation {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 0;
}

.command-explanation code {
  background: #e9ecef;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  color: #495057;
}

/* 架构模式样式 */
.architecture-patterns {
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 2rem 0;
}

.pattern-detail {
  margin-bottom: 3rem;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.pattern-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.pattern-icon {
  font-size: 2rem;
}

.pattern-header h4 {
  margin: 0;
  flex: 1;
  font-size: 1.3rem;
}

.severity-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.severity-badge.danger {
  background: rgba(244, 67, 54, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.severity-badge.success {
  background: rgba(76, 175, 80, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.pattern-content {
  padding: 2rem;
}

/* 分裂包演示样式 */
.split-package-example {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 1.5rem 0;
  justify-content: center;
}

.module-box {
  background: #e3f2fd;
  border: 2px solid #2196f3;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  min-width: 150px;
}

.module-box h5 {
  margin: 0 0 0.5rem 0;
  color: #1976d2;
}

.package-item {
  background: #fff;
  padding: 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #333;
}

.conflict-indicator {
  font-size: 2rem;
  color: #f44336;
  font-weight: bold;
}

.problem-explanation {
  background: #ffebee;
  border-left: 4px solid #f44336;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 4px;
}

.solution-box {
  background: #e8f5e8;
  border-left: 4px solid #4caf50;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 4px;
}

.solution-box h6 {
  margin: 0 0 0.5rem 0;
  color: #2e7d32;
}

.solution-box ul {
  margin: 0.5rem 0 0 1rem;
  color: #388e3c;
}

/* 多版本JAR样式 */
.mr-jar-structure {
  margin-top: 1rem;
}

.file-tree {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  margin: 1rem 0;
}

.tree-item {
  margin: 0.25rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tree-item.level-1 {
  margin-left: 1rem;
}

.tree-item.level-2 {
  margin-left: 2rem;
}

.tree-item.level-3 {
  margin-left: 3rem;
}

.tree-item.level-4 {
  margin-left: 4rem;
}

.version-tag {
  background: #4caf50;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  margin-left: auto;
}

.mr-jar-explanation {
  color: #666;
  font-style: italic;
  line-height: 1.6;
  margin-top: 1rem;
}

/* jlink展示样式 */
.jlink-showcase {
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 2rem 0;
}

.jlink-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.traditional-deployment,
.jlink-deployment {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.traditional-deployment h4,
.jlink-deployment h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.deployment-stack {
  margin: 1rem 0;
}

.layer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin: 0.5rem 0;
  border-radius: 6px;
  font-weight: 500;
}

.jre-layer {
  background: #ffcdd2;
  border: 2px solid #f44336;
}

.custom-runtime {
  background: #c8e6c9;
  border: 2px solid #4caf50;
}

.app-layer {
  background: #e1f5fe;
  border: 2px solid #03a9f4;
}

.total-size {
  font-size: 1.2rem;
  font-weight: 600;
  padding: 0.75rem;
  border-radius: 6px;
  margin-top: 1rem;
}

.total-size.traditional {
  background: #ffebee;
  color: #c62828;
}

.total-size.optimized {
  background: #e8f5e8;
  color: #2e7d32;
}

.savings {
  background: #4caf50;
  color: white;
  padding: 0.5rem;
  border-radius: 6px;
  margin-top: 0.5rem;
  font-weight: 600;
}

.jlink-commands {
  margin-top: 2rem;
}

.command-example {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

.jlink-benefits {
  margin-top: 2rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
  font-size: 1.5rem;
}

.benefit-item strong {
  display: block;
  color: #333;
  margin-bottom: 0.25rem;
}

.benefit-item small {
  color: #666;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .jlink-comparison {
    grid-template-columns: 1fr;
  }

  .split-package-example {
    flex-direction: column;
  }

  .conflict-indicator {
    transform: rotate(90deg);
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }
}

/* 章节总结样式 */
.chapter-summary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin: 2rem 0;
}

.summary-content {
  padding: 2rem;
}

.key-takeaways {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.takeaway-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.takeaway-item:hover {
  transform: translateY(-2px);
}

.takeaway-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.takeaway-item h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.takeaway-item p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

/* 思维导图样式 */
.mindmap-container {
  margin-top: 3rem;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.mindmap-container h3 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
}

.mindmap-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.mermaid-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 不隐藏pre标签，让Mermaid能够读取内容 */
.mermaid-diagram pre.mermaid {
  visibility: hidden; /* 使用visibility而不是display，保持元素存在但不可见 */
  position: absolute;
  top: -9999px;
}

/* Mermaid图表样式覆盖 */
.mindmap-wrapper .mermaid {
  max-width: 100%;
  height: auto;
}

.mindmap-wrapper svg {
  max-width: 100%;
  height: auto;
}

@media (max-width: 768px) {
  .key-takeaways {
    grid-template-columns: 1fr;
  }

  .mindmap-wrapper {
    min-height: 400px;
  }

  .takeaway-item {
    flex-direction: column;
    text-align: center;
  }

  .takeaway-icon {
    align-self: center;
  }

  /* 项目实践踩坑响应式 */
  .problem-cards {
    grid-template-columns: 1fr;
  }

  .solutions-grid {
    grid-template-columns: 1fr;
  }

  .pros-cons {
    grid-template-columns: 1fr;
  }

  .solution-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* 项目实践踩坑与解决方案样式 */
.real-world-problems {
  margin-top: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  border-radius: 12px;
  border-left: 5px solid #ff9800;
}

.real-world-problems h3 {
  margin: 0 0 2rem 0;
  color: #e65100;
  font-size: 1.5rem;
  font-weight: 700;
}

.problem-section,
.root-cause-analysis,
.solutions-section {
  margin-bottom: 2.5rem;
}

.problem-section h4,
.root-cause-analysis h4,
.solutions-section h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.problem-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.problem-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #f44336;
}

.problem-card.warning {
  border-left-color: #ff9800;
}

.problem-card.critical {
  border-left-color: #f44336;
}

.problem-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.problem-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.problem-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.problem-content p {
  margin: 0.75rem 0;
  line-height: 1.6;
  color: #555;
}

.problem-content code {
  background: #f5f5f5;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.cause-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cause-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.cause-item h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.cause-item p {
  margin: 0;
  line-height: 1.6;
  color: #555;
}

.solutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.solution-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-top: 4px solid #4caf50;
}

.solution-card.alternative {
  border-top-color: #2196f3;
}

.solution-card.temporary {
  border-top-color: #ff9800;
}

.solution-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.solution-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.solution-icon {
  font-size: 1.5rem;
}

.solution-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.solution-badge.best {
  background: #e8f5e8;
  color: #2e7d32;
}

.solution-badge.stable {
  background: #e3f2fd;
  color: #1565c0;
}

.solution-badge.conservative {
  background: #f3e5f5;
  color: #7b1fa2;
}

.solution-content p {
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
  color: #555;
}

.pros-cons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.pros,
.cons {
  padding: 1rem;
  border-radius: 8px;
}

.pros {
  background: #e8f5e8;
  border-left: 3px solid #4caf50;
}

.cons {
  background: #ffebee;
  border-left: 3px solid #f44336;
}

.pros h6,
.cons h6 {
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.pros ul,
.cons ul {
  margin: 0;
  padding-left: 1.2rem;
  list-style-type: disc;
}

.pros li,
.cons li {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.trade-offs {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #6c757d;
}

.trade-offs p {
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #495057;
}
</style>
